#if os(macOS)
import Foundation
import WebKit
import os.log
import Cocoa
import CLingXiaFFI

private let lxAppViewControllerLog = OSLog(subsystem: "com.lingxia.app", category: "LxAppView")

@MainActor
public class macOSLxAppViewController: NSViewController, WKNavigationDelegate {
    nonisolated(unsafe) private static let log = lxAppViewControllerLog

    // MARK: - Constants
    private static let TAB_BAR_HEIGHT: CGFloat = 40
    internal static let DEFAULT_NAV_BAR_HEIGHT: CGFloat = 32 // This constant is no longer used for layout, but kept for reference if needed elsewhere

    // MARK: - Properties
    internal var appId: String
    private var initialPath: String
    private var webViewContainer: NSView!
    private var tabBarView: NSView?
    private var currentWebView: WKWebView?
    private var tabBarConfig: TabBarConfig!
    // Removed: private var navigationBar: LingXiaNavigationBar!

    nonisolated(unsafe) private var closeAppObserver: NSObjectProtocol?
    nonisolated(unsafe) private var switchPageObserver: NSObjectProtocol?

    public init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        os_log("Deinitializing LxAppViewController for appId: %@", log: Self.log, type: .info, appId)
        if let observer = closeAppObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = switchPageObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    // MARK: - Lifecycle
    public override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = AppKit.NSColor.windowBackgroundColor.cgColor
    }

    public override func viewDidLoad() {
        super.viewDidLoad()
        os_log("ViewDidLoad started for appId: %@", log: Self.log, type: .info, appId)
        
        // Set view background color for visibility
        view.wantsLayer = true
        view.layer?.backgroundColor = AppKit.NSColor.windowBackgroundColor.cgColor
        
        // Ensure view size matches window content view
        if let window = view.window, let contentView = window.contentView {
            view.frame = contentView.bounds
        }
        
        // Setup UI components
        setupLayout()
        setupNotificationObservers()
        loadWebViewContent()
        
        // Force layout update
        view.needsLayout = true
        view.layoutSubtreeIfNeeded()
        
        os_log("ViewDidLoad completed", log: Self.log, type: .info)
    }

    // MARK: - UI Setup
    private func setupLayout() {
        os_log("Setting up UI layout for appId: %@", log: Self.log, type: .info, appId)
        
        // Set main view background
        view.wantsLayer = true
        view.layer?.backgroundColor = AppKit.NSColor.windowBackgroundColor.cgColor
        
        // Ensure view size is correct
        if let window = view.window, let contentView = window.contentView {
            view.frame = contentView.bounds
        }
        
        // Create TabBar first
        setupTabBar()
        
        // Create WebView container
        setupWebViewContainer()
        
        // Add TabBar to view hierarchy and set constraints based on position and transparency
        if let tabBar = tabBarView, let tabBarConfigRust = getTabBarConfig(appId), let tabBarConfig = TabBarConfig.fromJson(tabBarConfigRust.toString()) {
            view.addSubview(tabBar)

            // Check if TabBar is transparent
            let isTabBarTransparent = tabBarConfig.backgroundColor?.lowercased() == "transparent" || tabBarConfig.backgroundColor?.isEmpty == true
            os_log("[TabBar] isTabBarTransparent: %@", log: Self.log, type: .info, isTabBarTransparent ? "true" : "false")

            // Get TabBar height from constants
            let tabBarHeight: CGFloat = Self.TAB_BAR_HEIGHT

            // Set TabBar position based on config - support all four positions
            var tabBarConstraints: [NSLayoutConstraint] = []

            switch tabBarConfig.position ?? .bottom {
            case .bottom:
                tabBarConstraints = [
                    tabBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    tabBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    tabBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                    tabBar.heightAnchor.constraint(equalToConstant: tabBarHeight)
                ]

            case .top:
                tabBarConstraints = [
                    tabBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    tabBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    tabBar.topAnchor.constraint(equalTo: view.topAnchor),
                    tabBar.heightAnchor.constraint(equalToConstant: tabBarHeight)
                ]

            case .left:
                tabBarConstraints = [
                    tabBar.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    tabBar.topAnchor.constraint(equalTo: view.topAnchor),
                    tabBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                    tabBar.widthAnchor.constraint(equalToConstant: 80) // Same width as independent implementation
                ]

            case .right:
                tabBarConstraints = [
                    tabBar.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    tabBar.topAnchor.constraint(equalTo: view.topAnchor),
                    tabBar.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                    tabBar.widthAnchor.constraint(equalToConstant: 80) // Same width as independent implementation
                ]
            }

            NSLayoutConstraint.activate(tabBarConstraints)
            os_log("[TabBar] Activated TabBar constraints for position: %@", log: Self.log, type: .info, String(describing: tabBarConfig.position))

            // Set WebView container constraints based on TabBar position and transparency
            var webViewConstraints: [NSLayoutConstraint] = []

            if !isTabBarTransparent {
                // Non-transparent TabBar: WebView avoids TabBar area
                switch tabBarConfig.position ?? .bottom {
                case .bottom:
                    webViewConstraints = [
                        webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                        webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                        webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                        webViewContainer.bottomAnchor.constraint(equalTo: tabBar.topAnchor)
                    ]

                case .top:
                    webViewConstraints = [
                        webViewContainer.topAnchor.constraint(equalTo: tabBar.bottomAnchor),
                        webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                        webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                        webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
                    ]

                case .left:
                    webViewConstraints = [
                        webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                        webViewContainer.leadingAnchor.constraint(equalTo: tabBar.trailingAnchor),
                        webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                        webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
                    ]

                case .right:
                    webViewConstraints = [
                        webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                        webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                        webViewContainer.trailingAnchor.constraint(equalTo: tabBar.leadingAnchor),
                        webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
                    ]
                }
            } else {
                // Transparent TabBar: WebView extends full area, TabBar overlays
                webViewConstraints = [
                    webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                    webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                    webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                    webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
                ]
            }

            NSLayoutConstraint.activate(webViewConstraints)
            os_log("[TabBar] WebView container constrained for position: %@ (transparent: %@)", log: Self.log, type: .info, String(describing: tabBarConfig.position), isTabBarTransparent ? "true" : "false")
        } else {
            // No TabBar, WebView container takes full height
            NSLayoutConstraint.activate([
                webViewContainer.topAnchor.constraint(equalTo: view.topAnchor),
                webViewContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                webViewContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                webViewContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])
        }
        
        // Force layout update
        view.needsLayout = true
        view.layoutSubtreeIfNeeded()
    }
    
    private func addDebugLabel() {
        let label = NSTextField(labelWithString: "Debug - \(appId)")
        label.translatesAutoresizingMaskIntoConstraints = false
        label.textColor = AppKit.NSColor.white
        label.backgroundColor = AppKit.NSColor.black
        label.alignment = .center
        label.font = NSFont.boldSystemFont(ofSize: 16)
        
        view.addSubview(label)
        
        NSLayoutConstraint.activate([
            label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            label.topAnchor.constraint(equalTo: view.topAnchor, constant: 40),
            label.widthAnchor.constraint(equalToConstant: 200),
            label.heightAnchor.constraint(equalToConstant: 30)
        ])
    }

    // Removed: private func setupNavigationBar() { ... }

    private func setupWebViewContainer() {
        webViewContainer = NSView()
        webViewContainer.wantsLayer = true
        webViewContainer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(webViewContainer)
    }

    private func setupTabBar() {
        guard let tabBarConfigRust = getTabBarConfig(appId) else {
            os_log("No TabBar config found for appId: %@", log: Self.log, type: .info, appId)
            return
        }

        let tabBarConfigJson = tabBarConfigRust.toString()
        os_log("TabBar config JSON: %@", log: Self.log, type: .debug, tabBarConfigJson)

        guard let tabBarConfig = TabBarConfig.fromJson(tabBarConfigJson) else {
            os_log("Failed to parse TabBar config for appId: %@", log: Self.log, type: .error, appId)
            return
        }

        // Store config as instance property
        self.tabBarConfig = tabBarConfig

        // Check if TabBar is hidden in config
        if tabBarConfig.hidden == true {
            os_log("TabBar is hidden in config", log: Self.log, type: .info)
            return
        }

        // Create macOS TabBar with proper implementation
        let tabBar = NSView()
        tabBar.wantsLayer = true

        // Set background color from config - using same logic as independent implementation
        os_log("[TabBar] Setting background color", log: Self.log, type: .info)
        if let backgroundColor = tabBarConfig.backgroundColor {
            os_log("[TabBar] backgroundColor from config: %@", log: Self.log, type: .info, backgroundColor)
            let isTransparent = backgroundColor.lowercased() == "transparent" || backgroundColor.isEmpty
            os_log("[TabBar] isTransparent check result: %@", log: Self.log, type: .info, isTransparent ? "true" : "false")

            if isTransparent {
                os_log("[TabBar] Setting transparent background", log: Self.log, type: .info)
                tabBar.layer?.backgroundColor = NSColor.clear.cgColor
            } else {
                let parsedColor = NSColor(hexString: backgroundColor)
                os_log("[TabBar] Parsed color from hex '%@': %@", log: Self.log, type: .info, backgroundColor, parsedColor?.description ?? "nil")
                let finalColor = parsedColor?.cgColor ?? NSColor.controlBackgroundColor.cgColor
                os_log("[TabBar] Setting background color to: %@", log: Self.log, type: .info, String(describing: finalColor))
                tabBar.layer?.backgroundColor = finalColor
            }
        } else {
            os_log("[TabBar] No backgroundColor in config, using default", log: Self.log, type: .info)
            tabBar.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        }

        tabBar.translatesAutoresizingMaskIntoConstraints = false

        // Add tab buttons with orientation based on position
        let stackView = NSStackView()

        // Set orientation and spacing based on TabBar position
        switch tabBarConfig.position ?? .bottom {
        case .left, .right:
            stackView.orientation = .vertical
            stackView.distribution = .equalSpacing  // Same as independent implementation
            stackView.spacing = 10  // Same spacing as independent implementation
        case .top, .bottom:
            stackView.orientation = .horizontal
            stackView.distribution = .fillEqually
            stackView.spacing = 8  // Standard spacing for horizontal layout
        }

        stackView.translatesAutoresizingMaskIntoConstraints = false

        for (index, item) in tabBarConfig.list.enumerated() {
            let button = NSButton()
            button.title = item.text ?? ""
            button.font = NSFont.systemFont(ofSize: 10, weight: .medium)
            button.isBordered = false
            button.wantsLayer = true
            button.layer?.backgroundColor = NSColor.clear.cgColor
            button.tag = index
            button.target = self
            button.action = #selector(tabButtonTapped(_:))
            button.translatesAutoresizingMaskIntoConstraints = false

            // Set colors from config using the same method as independent implementation
            let isSelected = item.pagePath == initialPath
            button.contentTintColor = getTabColor(selected: isSelected)

            // Set icon if available
            if let iconPath = item.iconPath {
                setButtonIcon(button: button, iconPath: iconPath, isSelected: isSelected, item: item)
            }

            // Configure button layout based on TabBar position
            switch tabBarConfig.position ?? .bottom {
            case .left, .right:
                // For vertical TabBar, use same layout as independent implementation
                button.imagePosition = .imageAbove
                button.imageScaling = .scaleProportionallyDown
                button.font = NSFont.systemFont(ofSize: 10, weight: .medium)
                // Set fixed height for vertical buttons (same as independent implementation)
                button.heightAnchor.constraint(equalToConstant: 50).isActive = true

            case .top, .bottom:
                // For horizontal TabBar, use standard layout
                button.imagePosition = .imageAbove
                button.imageScaling = .scaleProportionallyDown
                button.font = NSFont.systemFont(ofSize: 10, weight: .medium)
            }

            stackView.addArrangedSubview(button)
        }

        tabBar.addSubview(stackView)

        // Set StackView constraints based on TabBar position
        switch tabBarConfig.position ?? .bottom {
        case .left, .right:
            // For vertical TabBar, use centerY constraint (same as independent implementation)
            NSLayoutConstraint.activate([
                stackView.leadingAnchor.constraint(equalTo: tabBar.leadingAnchor, constant: 4), // Reduced inset like independent implementation
                stackView.trailingAnchor.constraint(equalTo: tabBar.trailingAnchor, constant: -4),
                stackView.centerYAnchor.constraint(equalTo: tabBar.centerYAnchor)
            ])

        case .top, .bottom:
            // For horizontal TabBar, fill the entire area
            NSLayoutConstraint.activate([
                stackView.leadingAnchor.constraint(equalTo: tabBar.leadingAnchor, constant: 16),
                stackView.trailingAnchor.constraint(equalTo: tabBar.trailingAnchor, constant: -16),
                stackView.topAnchor.constraint(equalTo: tabBar.topAnchor),
                stackView.bottomAnchor.constraint(equalTo: tabBar.bottomAnchor)
            ])
        }

        self.tabBarView = tabBar



        os_log("✅ TabBar setup completed with %d items", log: Self.log, type: .info, tabBarConfig.list.count)
    }



    private func loadWebViewContent() {
        os_log("Loading WebView content for appId: %@ path: %@", log: Self.log, type: .info, appId, initialPath)
        
        // Try to find WebView - WebView is created by Rust
        if let webView = SharedWebViewManager.findWebView(appId: appId, path: initialPath) {
            attachWebViewToContainer(webView)
        } else {
            os_log("WebView not found for appId: %@ path: %@", log: Self.log, type: .info, appId, initialPath)
        }
        
        // Force layout update
        webViewContainer.needsLayout = true
        webViewContainer.layoutSubtreeIfNeeded()
    }
    

    
    private func attachWebViewToContainer(_ webView: WKWebView) {
        currentWebView?.removeFromSuperview()
        currentWebView = webView
        
        webView.translatesAutoresizingMaskIntoConstraints = false
        webViewContainer.addSubview(webView)
        
        NSLayoutConstraint.activate([
            webView.topAnchor.constraint(equalTo: webViewContainer.topAnchor),
            webView.leadingAnchor.constraint(equalTo: webViewContainer.leadingAnchor),
            webView.trailingAnchor.constraint(equalTo: webViewContainer.trailingAnchor),
            webView.bottomAnchor.constraint(equalTo: webViewContainer.bottomAnchor)
        ])
        
        // Force layout update - use macOS compatible method
        #if os(macOS)
        webView.needsLayout = true
        webViewContainer.needsLayout = true
        webViewContainer.layoutSubtreeIfNeeded()
        #else
        // iOS version
        webView.setNeedsLayout()
        webView.layoutIfNeeded()
        webViewContainer.setNeedsLayout()
        webViewContainer.layoutIfNeeded()
        #endif
        
        // Ensure WebView is visible
        webView.isHidden = false
        #if os(iOS)
        webView.alpha = 1.0
        #endif
        
        webView.navigationDelegate = self
        let _ = onPageShow(appId, initialPath)
        os_log("WebView attached to container", log: Self.log, type: .info)
    }

    private func setupNotificationObservers() {
        closeAppObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_CLOSE_LXAPP), object: nil, queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            Task { @MainActor in
                guard let self = self, let targetAppId = appId, targetAppId == self.appId else { return }
                os_log("Received close request for appId: %@", log: Self.log, type: .info, self.appId)
                self.view.window?.close()
            }
        }

        switchPageObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name(ACTION_SWITCH_PAGE), object: nil, queue: .main
        ) { [weak self] notification in
            let appId = notification.userInfo?["appId"] as? String
            let path = notification.userInfo?["path"] as? String
            Task { @MainActor in
                guard let self = self, let targetAppId = appId, let targetPath = path, targetAppId == self.appId else { return }
                os_log("Received switch page notification - appId: %@ path: %@", log: Self.log, type: .info, self.appId, targetPath)
                self.switchPage(targetPath: targetPath)
            }
        }
        os_log("Notification observers set up for appId: %@", log: Self.log, type: .info, appId)
    }

    // MARK: - Page Switching
    public func switchPage(targetPath: String) {
        guard !appId.isEmpty else {
            os_log("Cannot switch page: appId not initialized", log: Self.log, type: .error)
            return
        }

        // Check if trying to navigate to current page
        if currentWebView?.currentPath == targetPath {
            return
        }

        self.initialPath = targetPath
        os_log("Switching to page: %@", log: Self.log, type: .info, targetPath)

        // Check if this is a tab page
        if let _ = tabBarView?.subviews.first as? NSStackView,
           let tabIndex = findTabIndexByPath(targetPath), tabIndex >= 0 {
            switchToTab(targetPath: targetPath, tabIndex: tabIndex)
        } else {
            // Handle non-tab page navigation
            navigateToPage(targetPath: targetPath)
        }

        LxAppCore.setLastActivePath(targetPath, for: appId)
    }



    // MARK: - Helper Methods
    private func findTabIndexByPath(_ targetPath: String) -> Int? {
        guard let tabBarConfig = tabBarConfig else { return nil }

        for (index, item) in tabBarConfig.list.enumerated() {
            if item.pagePath == targetPath {
                return index
            }
        }
        return nil
    }

    private func switchToTab(targetPath: String, tabIndex: Int) {
        // Capture reference to previous WebView
        let previousWebView = currentWebView
        os_log("switchToTab: Previous WebView path=%@", log: Self.log, type: .info, previousWebView?.currentPath ?? "nil")

        // Find target WebView (should be created by Rust layer when needed)
        guard let targetWebView = SharedWebViewManager.findWebView(appId: appId, path: targetPath) else {
            os_log("switchToTab failed: WebView not found for %@, should be created by Rust system", log: Self.log, type: .error, targetPath)
            return
        }

        // Set current WebView to target for tracking
        currentWebView = targetWebView

        // Update TabBar UI (without triggering listener)
        updateTabBarSelection(selectedIndex: tabIndex)

        os_log("switchToTab: Attaching WebView", log: Self.log, type: .info)
        attachWebViewToContainer(targetWebView)

        // Hide previous WebView
        if let previousWebView = previousWebView, previousWebView != targetWebView {
            previousWebView.isHidden = true
        }

        // Trigger onPageShow after WebView is attached
        let _ = onPageShow(appId, targetPath)
    }

    private func navigateToPage(targetPath: String) {
        // Find WebView for the target page
        guard let newWebView = SharedWebViewManager.findWebView(appId: appId, path: targetPath) else {
            os_log("WebView not found for path: %@, should be created by Rust system", log: Self.log, type: .info, targetPath)
            return
        }

        // Update current WebView reference
        currentWebView = newWebView

        // Attach WebView to container
        attachWebViewToContainer(newWebView)

        // Trigger onPageShow after WebView is attached
        let _ = onPageShow(appId, targetPath)
    }

    // MARK: - TabBar Actions
    private func updateTabBarSelection(selectedIndex: Int) {
        guard let stackView = tabBarView?.subviews.first as? NSStackView else { return }

        for (buttonIndex, arrangedSubview) in stackView.arrangedSubviews.enumerated() {
            if let button = arrangedSubview as? NSButton {
                let isSelected = buttonIndex == selectedIndex

                // Update button color using the same method as independent implementation
                button.contentTintColor = getTabColor(selected: isSelected)

                // Update icon if needed
                let configItem = tabBarConfig.list[buttonIndex]
                if let iconPath = configItem.iconPath {
                    setButtonIcon(button: button, iconPath: iconPath, isSelected: isSelected, item: configItem)
                }
            }
        }
    }

    @objc private func tabButtonTapped(_ sender: NSButton) {
        let index = sender.tag
        guard index >= 0 && index < tabBarConfig.list.count else { return }

        let item = tabBarConfig.list[index]
        os_log("Tab button tapped: %@ -> %@", log: Self.log, type: .info, item.text ?? "Unknown", item.pagePath)

        // Switch to the selected page
        switchPage(targetPath: item.pagePath)
    }

    // MARK: - Helpers
    private func getResourcesPath() -> String {
        let executablePath = Bundle.main.executablePath ?? ""
        let executableDir = (executablePath as NSString).deletingLastPathComponent
        return "\(executableDir)/Resources"
    }

    private func getTabColor(selected: Bool) -> NSColor {
        if selected {
            if let selectedColor = tabBarConfig.selectedColor {
                return NSColor(hexString: selectedColor) ?? NSColor.controlAccentColor
            }
            return NSColor.controlAccentColor
        } else {
            if let color = tabBarConfig.color {
                return NSColor(hexString: color) ?? NSColor.secondaryLabelColor
            }
            return NSColor.secondaryLabelColor
        }
    }

    private func setButtonIcon(button: NSButton, iconPath: String, isSelected: Bool, item: TabBarItem) {
        var image: NSImage?

        // Use selected icon if available and selected
        let actualIconPath = (isSelected && item.selectedIconPath != nil) ? item.selectedIconPath! : iconPath

        if actualIconPath.hasPrefix("SF:") {
            // System SF Symbol
            let symbolName = String(actualIconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)
                image?.isTemplate = true
            }
        } else if actualIconPath.hasPrefix("/") {
            // Absolute path
            image = NSImage(contentsOfFile: actualIconPath)
        } else {
            // Try bundle first
            image = NSImage(named: actualIconPath)

            // If not found in bundle, try with appId in Resources directory
            if image == nil && !appId.isEmpty {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/\(appId)/\(actualIconPath)"
                image = NSImage(contentsOfFile: fullPath)
                os_log("Loading icon from: %@", log: Self.log, type: .debug, fullPath)
            }
        }

        if let image = image {
            let iconSize: CGFloat = 24
            let resizedImage = resizeImage(image, to: NSSize(width: iconSize, height: iconSize))
            button.image = resizedImage
        } else {
            os_log("Failed to load icon: %@", log: Self.log, type: .error, actualIconPath)
        }
    }

    private func resizeImage(_ image: NSImage, to size: NSSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        
        // Draw image to fit size
        let drawRect = NSRect(origin: .zero, size: size)
        image.draw(in: drawRect)
        
        resizedImage.unlockFocus()
        resizedImage.isTemplate = image.isTemplate
        
        return resizedImage
    }



    // Helper method to check if a color is transparent
    private func isTransparentColor(_ color: NSColor) -> Bool {
        // Convert to calibrated RGB color space to access components
        let rgbColor = color.usingColorSpace(.sRGB) ?? color
        return rgbColor.alphaComponent < 0.1
    }
    
    // Helper method to check if a color string represents transparency
    private func isTransparentColor(_ colorString: String) -> Bool {
        return colorString.lowercased() == "transparent" || colorString.isEmpty
    }
}

// MARK: - LingXiaNavigationBar Class (No longer used for main navigation bar in macOS)
public class LingXiaNavigationBar: NSView {
    private static let TITLE_FONT_SIZE: CGFloat = 17
    private static let TITLE_FONT_WEIGHT: NSFont.Weight = .medium
    private static let BACKGROUND_COLOR = NSColor(red: 0.93, green: 0.93, blue: 0.93, alpha: 1.0)
    private static let BORDER_COLOR = NSColor(red: 0.87, green: 0.87, blue: 0.87, alpha: 1.0)

    private var titleLabel: NSTextField!

    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = Self.BACKGROUND_COLOR.cgColor

        let bottomBorder = NSView()
        bottomBorder.wantsLayer = true
        bottomBorder.layer?.backgroundColor = Self.BORDER_COLOR.cgColor
        bottomBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(bottomBorder)

        titleLabel = NSTextField(labelWithString: "")
        titleLabel.font = NSFont.systemFont(ofSize: Self.TITLE_FONT_SIZE, weight: Self.TITLE_FONT_WEIGHT)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        addSubview(titleLabel)

        NSLayoutConstraint.activate([
            bottomBorder.leadingAnchor.constraint(equalTo: leadingAnchor),
            bottomBorder.trailingAnchor.constraint(equalTo: trailingAnchor),
            bottomBorder.bottomAnchor.constraint(equalTo: bottomAnchor),
            bottomBorder.heightAnchor.constraint(equalToConstant: 0.5),
            
            titleLabel.centerXAnchor.constraint(equalTo: centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: centerYAnchor)
        ])
    }

    public func setTitle(_ title: String) {
        titleLabel.stringValue = title
    }
}


#endif