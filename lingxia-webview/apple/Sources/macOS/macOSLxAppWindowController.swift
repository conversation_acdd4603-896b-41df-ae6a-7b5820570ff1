import lingxia
import Cocoa
import WebKit
import os.log

// Weak reference wrapper for window controller management
private class WeakRef<T: AnyObject> {
    weak var value: T?
    init(_ value: T) {
        self.value = value
    }
}

private let lxAppWindowControllerLog = OSLog(subsystem: "LingXia", category: "LxAppWindow")

@MainActor
public class macOSLxAppWindowController: NSWindowController {
    private static let log = lxAppWindowControllerLog

    // Window size configuration
    private static var windowSize: (width: CGFloat, height: CGFloat) = (600, 800)
    private static var isWindowSizeFixed: Bool = false
    private static var allWindowControllers: [WeakRef<macOSLxAppWindowController>] = []

    internal var appId: String
    private var initialPath: String
    private var lxAppViewController: macOSLxAppViewController!
    private var customTitleBarView: NSView!
    private let customTitleBarHeight: CGFloat = 32

    /// Set window size for all LxApp windows (existing and new)
    /// - Parameters:
    ///   - width: Window width
    ///   - height: Window height
    ///   - fixed: If true, prevents user from resizing the window
    public static func setWindowSize(width: CGFloat, height: CGFloat, fixed: Bool = false) {
        os_log("🔧 setWindowSize called: %fx%f, fixed: %@", log: log, type: .info,
               width, height, fixed ? "true" : "false")

        windowSize = (width, height)
        isWindowSizeFixed = fixed

        os_log("📊 Current window controllers count: %d", log: log, type: .info, allWindowControllers.count)

        // Clean up deallocated references and apply to existing windows
        allWindowControllers = allWindowControllers.compactMap { weakRef in
            guard let controller = weakRef.value else {
                os_log("🗑️ Removing deallocated window controller", log: log, type: .info)
                return nil
            }
            os_log("🔄 Applying size to existing window controller", log: log, type: .info)
            controller.applyWindowSize()
            return weakRef
        }

        os_log("✅ setWindowSize completed", log: log, type: .info)
    }

    init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path

        // Use current window size configuration
        let contentRect = NSRect(x: 100, y: 100, width: Self.windowSize.width, height: Self.windowSize.height)
        
        // Basic window style - resizability will be set by applyWindowSize()
        let styleMask: NSWindow.StyleMask = [.titled, .closable, .miniaturizable]

        let window = NSWindow(
            contentRect: contentRect,
            styleMask: styleMask,
            backing: .buffered,
            defer: false
        )
        
        super.init(window: window)
        
        os_log("Window controller initializing for appId=%@", log: Self.log, type: .info, appId)

        // Setup window properties
        setupWindow()
        
        // Skip custom title bar for better macOS integration
        // Use standard macOS window controls instead
        
        // Setup view controller
        setupViewController()

        // Add to static array for window size management
        Self.allWindowControllers.append(WeakRef(self))

        os_log("Window controller initialized for appId=%@", log: Self.log, type: .info, appId)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }


    
    private func setupWindow() {
        guard let window = window else { return }
        
        // Set window title
        window.title = "LingXia - \(appId)"
        window.isReleasedWhenClosed = false
        window.delegate = self
        
        // Use standard title bar for better macOS integration
        window.titlebarAppearsTransparent = false
        window.titleVisibility = .visible
        window.styleMask.remove(.fullSizeContentView)
        window.isMovableByWindowBackground = false
        
        // Disable window tabbing
        NSWindow.allowsAutomaticWindowTabbing = false

        // Ensure window visibility
        window.alphaValue = 1.0
        window.level = .normal

        // Show standard window buttons
        window.standardWindowButton(.closeButton)?.isHidden = false
        window.standardWindowButton(.miniaturizeButton)?.isHidden = false
        window.standardWindowButton(.zoomButton)?.isHidden = false

        // Set minimum window size constraints
        window.minSize = NSSize(width: 400, height: 600)

        // Set window background color
        window.backgroundColor = NSColor.windowBackgroundColor

        // Apply current window size configuration
        applyWindowSize()

        // Center window on screen
        window.center()
        os_log("🖥️ Window centered at: %@", log: Self.log, type: .info, NSStringFromRect(window.frame))

        // Force window to front
        os_log("🔄 Making window key and ordering front...", log: Self.log, type: .info)
        window.makeKeyAndOrderFront(nil)
        window.orderFrontRegardless()
        NSApp.activate(ignoringOtherApps: true)

        // Check window visibility
        os_log("👁️ Window visible: %@, key: %@, main: %@", log: Self.log, type: .info,
               window.isVisible ? "YES" : "NO",
               window.isKeyWindow ? "YES" : "NO",
               window.isMainWindow ? "YES" : "NO")

        os_log("Window setup completed - frame: %@", log: Self.log, type: .info, NSStringFromRect(window.frame))
    }
    
    private func setupCustomTitleBar() {
        guard let contentView = window?.contentView else { return }

        customTitleBarView = NSView()
        customTitleBarView.wantsLayer = true
        customTitleBarView.layer?.backgroundColor = NSColor.white.cgColor
        customTitleBarView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(customTitleBarView)

        // Add shadow line at bottom
        let shadowView = NSView()
        shadowView.wantsLayer = true
        shadowView.layer?.backgroundColor = NSColor.black.withAlphaComponent(0.1).cgColor
        shadowView.translatesAutoresizingMaskIntoConstraints = false
        customTitleBarView.addSubview(shadowView)

        // Add title label
        let titleLabel = NSTextField(labelWithString: getWindowTitle())
        titleLabel.font = NSFont.systemFont(ofSize: 17, weight: .semibold)
        titleLabel.textColor = NSColor.black
        titleLabel.alignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        customTitleBarView.addSubview(titleLabel)

        // Create capsule buttons
        let moreButton = createStandardButton(image: createThreeDotsImage(), target: self, action: #selector(moreButtonTapped))
        moreButton.toolTip = "More"

        let minimizeButton = createStandardButton(image: createMinimizeButtonImage(), target: self, action: #selector(minimizeWindow))
        minimizeButton.toolTip = "Minimize"

        let closeButton = createStandardButton(image: createCloseButtonImage(), target: self, action: #selector(closeWindow))
        closeButton.toolTip = "Close"

        let stackView = NSStackView(views: [moreButton, minimizeButton, closeButton])
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        
        // Add separators between buttons
        let separator1 = NSView()
        separator1.wantsLayer = true
        separator1.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.4).cgColor
        separator1.translatesAutoresizingMaskIntoConstraints = false
        
        let separator2 = NSView()
        separator2.wantsLayer = true
        separator2.layer?.backgroundColor = NSColor.white.withAlphaComponent(0.4).cgColor
        separator2.translatesAutoresizingMaskIntoConstraints = false

        let capsuleContainer = NSVisualEffectView()
        capsuleContainer.blendingMode = .withinWindow
        capsuleContainer.material = .titlebar
        capsuleContainer.state = .active
        capsuleContainer.wantsLayer = true
        capsuleContainer.layer?.cornerRadius = 14 // Half of 28 (capsule button height)
        capsuleContainer.translatesAutoresizingMaskIntoConstraints = false
        
        capsuleContainer.addSubview(stackView)
        capsuleContainer.addSubview(separator1)
        capsuleContainer.addSubview(separator2)
        customTitleBarView.addSubview(capsuleContainer)

        // Constraints for customTitleBarView
        NSLayoutConstraint.activate([
            customTitleBarView.topAnchor.constraint(equalTo: contentView.topAnchor),
            customTitleBarView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            customTitleBarView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            customTitleBarView.heightAnchor.constraint(equalToConstant: customTitleBarHeight)
        ])

        // Constraints for shadowView
        NSLayoutConstraint.activate([
            shadowView.leadingAnchor.constraint(equalTo: customTitleBarView.leadingAnchor),
            shadowView.trailingAnchor.constraint(equalTo: customTitleBarView.trailingAnchor),
            shadowView.bottomAnchor.constraint(equalTo: customTitleBarView.bottomAnchor),
            shadowView.heightAnchor.constraint(equalToConstant: 1)
        ])

        // Constraints for titleLabel
        NSLayoutConstraint.activate([
            titleLabel.centerXAnchor.constraint(equalTo: customTitleBarView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: customTitleBarView.centerYAnchor)
        ])

        // Constraints for capsuleContainer
        let capsuleWidth: CGFloat = 87
        let capsuleHeight: CGFloat = 28
        let capsuleTopMargin: CGFloat = 2
        let capsuleRightMargin: CGFloat = 7

        NSLayoutConstraint.activate([
            capsuleContainer.widthAnchor.constraint(equalToConstant: capsuleWidth),
            capsuleContainer.heightAnchor.constraint(equalToConstant: capsuleHeight),
            capsuleContainer.topAnchor.constraint(equalTo: customTitleBarView.topAnchor, constant: capsuleTopMargin),
            capsuleContainer.trailingAnchor.constraint(equalTo: customTitleBarView.trailingAnchor, constant: -capsuleRightMargin)
        ])
        
        // Constraints for stackView inside capsuleContainer
        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: capsuleContainer.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: capsuleContainer.trailingAnchor),
            stackView.topAnchor.constraint(equalTo: capsuleContainer.topAnchor),
            stackView.bottomAnchor.constraint(equalTo: capsuleContainer.bottomAnchor)
        ])
        
        // Constraints for separators
        NSLayoutConstraint.activate([
            separator1.centerYAnchor.constraint(equalTo: stackView.centerYAnchor),
            separator1.centerXAnchor.constraint(equalTo: moreButton.trailingAnchor),
            separator1.heightAnchor.constraint(equalToConstant: capsuleHeight - 8),
            separator1.widthAnchor.constraint(equalToConstant: 1),
            
            separator2.centerYAnchor.constraint(equalTo: stackView.centerYAnchor),
            separator2.centerXAnchor.constraint(equalTo: minimizeButton.trailingAnchor),
            separator2.heightAnchor.constraint(equalToConstant: capsuleHeight - 8),
            separator2.widthAnchor.constraint(equalToConstant: 1)
        ])
    }

    /// Apply current window size configuration to this window
    private func applyWindowSize() {
        guard let window = window else {
            os_log("❌ No window available for applyWindowSize", log: Self.log, type: .error)
            return
        }

        os_log("🔧 Applying window size: %fx%f, fixed: %@", log: Self.log, type: .info,
               Self.windowSize.width, Self.windowSize.height, Self.isWindowSizeFixed ? "true" : "false")

        // Set window size
        let newSize = NSSize(width: Self.windowSize.width, height: Self.windowSize.height)
        window.setContentSize(newSize)

        // Configure resizability based on fixed setting
        os_log("🔒 Current styleMask before: %@", log: Self.log, type: .info, String(describing: window.styleMask))
        if Self.isWindowSizeFixed {
            os_log("🔒 Removing .resizable from window", log: Self.log, type: .info)
            window.styleMask.remove(.resizable)
        } else {
            os_log("🔓 Adding .resizable to window", log: Self.log, type: .info)
            window.styleMask.insert(.resizable)
        }
        os_log("🔒 Current styleMask after: %@", log: Self.log, type: .info, String(describing: window.styleMask))

        // Verify the window is actually not resizable
        let hasResizable = window.styleMask.contains(.resizable)
        os_log("🔍 Window has .resizable: %@", log: Self.log, type: .info, hasResizable ? "YES" : "NO")

        // Verify the size was set correctly
        let actualSize = window.frame.size
        os_log("✅ Window size applied. Actual frame size: %fx%f", log: Self.log, type: .info,
               actualSize.width, actualSize.height)
    }

    private func getWindowTitle() -> String {
        return "LingXia"
    }

    private func setupViewController() {
        guard let window = window else {
            os_log("❌ No window available for setupViewController", log: Self.log, type: .error)
            return
        }

        // Create view controller
        lxAppViewController = macOSLxAppViewController(appId: appId, path: initialPath)
        window.contentViewController = lxAppViewController
        
        // Skip custom title bar adjustments
        
        // Force layout update
        window.contentView?.needsLayout = true
        window.contentView?.layoutSubtreeIfNeeded()
        
        os_log("✅ setupViewController completed", log: Self.log, type: .info)
    }

    // MARK: - Button Actions
    @objc private func moreButtonTapped() {
        os_log("More button tapped", log: Self.log, type: .info)
        // TODO: Implement more options menu
    }

    @objc private func minimizeWindow() {
        window?.miniaturize(nil)
    }

    @objc private func closeWindow() {
        window?.close()
    }

    // MARK: - Button Creation Helpers
    private func createStandardButton(image: NSImage?, target: AnyObject?, action: Selector?) -> NSButton {
        let button = NSButton(image: image ?? NSImage(), target: target, action: action)
        button.bezelStyle = .regularSquare
        button.isBordered = false
        button.imagePosition = .imageOnly
        button.imageScaling = .scaleProportionallyDown
        button.wantsLayer = true
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.setButtonType(.momentaryPushIn)
        button.allowedTouchTypes = .direct
        button.translatesAutoresizingMaskIntoConstraints = false // Use Auto Layout
        return button
    }

    private func createThreeDotsImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setFillColor(NSColor.black.cgColor)

            let dotRadius: CGFloat = 1.5
            let dotSpacing: CGFloat = 4
            let totalWidth = 3 * (dotRadius * 2) + 2 * dotSpacing
            let startX = (size.width - totalWidth) / 2
            let centerY = size.height / 2

            for i in 0..<3 {
                let x = startX + CGFloat(i) * (dotRadius * 2 + dotSpacing) + dotRadius
                let dotRect = CGRect(x: x - dotRadius, y: centerY - dotRadius, width: dotRadius * 2, height: dotRadius * 2)
                context.fillEllipse(in: dotRect)
            }
        }

        image.unlockFocus()
        return image
    }

    private func createMinimizeButtonImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setStrokeColor(NSColor.black.cgColor)
            context.setLineWidth(1.5)

            let lineY = size.height / 2
            let lineStartX: CGFloat = 8
            let lineEndX: CGFloat = 16

            context.move(to: CGPoint(x: lineStartX, y: lineY))
            context.addLine(to: CGPoint(x: lineEndX, y: lineY))
            context.strokePath()
        }

        image.unlockFocus()
        return image
    }

    private func createCloseButtonImage() -> NSImage? {
        let size = CGSize(width: 24, height: 24)
        let image = NSImage(size: size)
        image.lockFocus()

        if let context = NSGraphicsContext.current?.cgContext {
            context.setStrokeColor(NSColor.black.cgColor)
            context.setLineWidth(1.5)

            let margin: CGFloat = 8
            let startPoint1 = CGPoint(x: margin, y: margin)
            let endPoint1 = CGPoint(x: size.width - margin, y: size.height - margin)
            let startPoint2 = CGPoint(x: size.width - margin, y: margin)
            let endPoint2 = CGPoint(x: margin, y: size.height - margin)

            context.move(to: startPoint1)
            context.addLine(to: endPoint1)
            context.move(to: startPoint2)
            context.addLine(to: endPoint2)
            context.strokePath()
        }

        image.unlockFocus()
        return image
    }
}

extension macOSLxAppWindowController: NSWindowDelegate {
    public func windowWillClose(_ notification: Notification) {
        os_log("Window will close for appId=%@", log: Self.log, type: .info, appId)
        macOSLxApp.removeWindowController(self)
    }
    
    public func windowDidBecomeKey(_ notification: Notification) {
        os_log("Window became key for appId=%@", log: Self.log, type: .info, appId)
    }
}