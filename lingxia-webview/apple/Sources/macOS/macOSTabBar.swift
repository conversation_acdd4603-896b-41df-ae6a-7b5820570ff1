#if os(macOS)
import Cocoa
import Foundation

/// Custom button that responds to clicks in the entire button area
class TabBarButton: NSButton {
    // Use NSButton's default hit testing for proper click detection
}

/// macOS TabBar implementation
@MainActor
public class macOSTabBar: NSView, TabBarProtocol {
    
    // MARK: - Properties
    public var onTabSelectedListener: ((String) -> Void)?

    private var config: TabBarConfig?
    private var appId: String = ""
    private var tabButtons: [NSButton] = []
    private var selectedIndex: Int = 0
    private var stackView: NSStackView?
    
    // MARK: - Initialization
    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        wantsLayer = true
        layer?.backgroundColor = TabBarConfig.DEFAULT_BACKGROUND_COLOR.cgColor
    }
    
    // MARK: - TabBarProtocol Implementation
    public func updateConfig(_ config: TabBarConfig, appId: String) {
        print("[macOSTabBar] updateConfig called with \(config.list.count) items for appId: \(appId)")
        self.config = config
        self.appId = appId

        isHidden = config.hidden ?? false

        setupTabButtons()
        updateAppearance()
    }
    
    public func setSelectedTab(_ pagePath: String) {
        guard let config = config else { return }
        
        for (index, item) in config.list.enumerated() {
            if item.pagePath == pagePath {
                setSelectedTab(index: index, notifyListener: false)
                break
            }
        }
    }
    
    // MARK: - Private Methods
    private func setupTabButtons() {
        // Remove existing buttons and stack view
        tabButtons.forEach { $0.removeFromSuperview() }
        tabButtons.removeAll()
        stackView?.removeFromSuperview()
        
        guard let config = config, !config.list.isEmpty else { return }
        
        // Create tab buttons
        for (index, item) in config.list.enumerated() {
            let button = createTabButton(for: item, at: index)
            tabButtons.append(button)
        }
        
        // Setup layout
        layoutTabButtons()
        
        // Set initial selection
        setSelectedTab(index: 0, notifyListener: false)
    }
    
    private func createTabButton(for item: TabBarItem, at index: Int) -> NSButton {
        let button = TabBarButton()
        button.title = item.text ?? ""
        button.font = NSFont.systemFont(ofSize: TabBarConstants.ITEM_FONT_SIZE, weight: .medium)
        button.isBordered = false
        button.target = self
        button.action = #selector(tabButtonTapped(_:))
        button.tag = index
        button.translatesAutoresizingMaskIntoConstraints = false
        
        // Style button
        button.wantsLayer = true
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.contentTintColor = getTabColor(selected: false)
        
        // Set icon if available
        if let iconPath = item.iconPath {
            setButtonIcon(button: button, iconPath: iconPath, selected: false)
        }
        
        // Set button layout
        button.imagePosition = .imageAbove
        button.imageScaling = .scaleProportionallyDown
        
        return button
    }
    
    private func layoutTabButtons() {
        guard !tabButtons.isEmpty else { return }
        
        let position = config?.position ?? .bottom
        let isVertical = position == .left || position == .right
        
        // Create stack view
        stackView = NSStackView(views: tabButtons)
        stackView!.translatesAutoresizingMaskIntoConstraints = false
        addSubview(stackView!)
        
        if isVertical {
            layoutVerticalTabs()
        } else {
            layoutHorizontalTabs()
        }
    }
    
    private func layoutHorizontalTabs() {
        guard let stackView = stackView else { return }
        
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8
        
        for button in tabButtons {
            NSLayoutConstraint.activate([
                button.heightAnchor.constraint(equalTo: heightAnchor)
            ])
        }
        
        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -16),
            stackView.topAnchor.constraint(equalTo: topAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    private func layoutVerticalTabs() {
        guard let stackView = stackView else { return }
        
        stackView.orientation = .vertical
        stackView.distribution = .equalSpacing
        stackView.spacing = 10
        
        let buttonHeight: CGFloat = 50
        let horizontalInset: CGFloat = 4
        
        for button in tabButtons {
            NSLayoutConstraint.activate([
                button.heightAnchor.constraint(equalToConstant: buttonHeight)
            ])
        }
        
        NSLayoutConstraint.activate([
            stackView.centerYAnchor.constraint(equalTo: centerYAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: horizontalInset),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -horizontalInset)
        ])
    }
    
    private func updateAppearance() {
        guard let config = config else { return }
        
        // Update background color
        if let backgroundColor = config.backgroundColor {
            let isTransparent = TabBarConfig.isTransparent(backgroundColor)
            
            if isTransparent {
                layer?.backgroundColor = NSColor.clear.cgColor
            } else {
                let color = NSColor(hexString: backgroundColor) ?? TabBarConfig.DEFAULT_BACKGROUND_COLOR
                layer?.backgroundColor = color.cgColor
            }
        } else {
            layer?.backgroundColor = TabBarConfig.DEFAULT_BACKGROUND_COLOR.cgColor
        }
        
        // Add border if needed
        if let borderStyle = config.borderStyle, borderStyle != "none" {
            addBorder()
        }
    }
    
    private func addBorder() {
        let position = config?.position ?? .bottom
        
        let borderView = NSView()
        borderView.wantsLayer = true
        borderView.layer?.backgroundColor = NSColor.separatorColor.cgColor
        borderView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(borderView)
        
        switch position {
        case .bottom:
            NSLayoutConstraint.activate([
                borderView.leadingAnchor.constraint(equalTo: leadingAnchor),
                borderView.trailingAnchor.constraint(equalTo: trailingAnchor),
                borderView.topAnchor.constraint(equalTo: topAnchor),
                borderView.heightAnchor.constraint(equalToConstant: 0.5)
            ])
        case .top:
            NSLayoutConstraint.activate([
                borderView.leadingAnchor.constraint(equalTo: leadingAnchor),
                borderView.trailingAnchor.constraint(equalTo: trailingAnchor),
                borderView.bottomAnchor.constraint(equalTo: bottomAnchor),
                borderView.heightAnchor.constraint(equalToConstant: 0.5)
            ])
        case .left:
            NSLayoutConstraint.activate([
                borderView.topAnchor.constraint(equalTo: topAnchor),
                borderView.bottomAnchor.constraint(equalTo: bottomAnchor),
                borderView.trailingAnchor.constraint(equalTo: trailingAnchor),
                borderView.widthAnchor.constraint(equalToConstant: 0.5)
            ])
        case .right:
            NSLayoutConstraint.activate([
                borderView.topAnchor.constraint(equalTo: topAnchor),
                borderView.bottomAnchor.constraint(equalTo: bottomAnchor),
                borderView.leadingAnchor.constraint(equalTo: leadingAnchor),
                borderView.widthAnchor.constraint(equalToConstant: 0.5)
            ])
        }
    }
    
    private func getTabColor(selected: Bool) -> NSColor {
        guard let config = config else {
            return selected ? TabBarConfig.DEFAULT_SELECTED_COLOR : TabBarConfig.DEFAULT_COLOR
        }
        
        if selected {
            if let selectedColor = config.selectedColor {
                return NSColor(hexString: selectedColor) ?? NSColor.controlAccentColor
            }
            return NSColor.controlAccentColor
        } else {
            if let color = config.color {
                return NSColor(hexString: color) ?? NSColor.secondaryLabelColor
            }
            return NSColor.secondaryLabelColor
        }
    }
    
    private func setSelectedTab(index: Int, notifyListener: Bool = true) {
        guard index >= 0 && index < tabButtons.count else { return }
        
        // Update previous selection
        if selectedIndex < tabButtons.count {
            let previousButton = tabButtons[selectedIndex]
            previousButton.contentTintColor = getTabColor(selected: false)
            
            // Update icon if needed
            if let item = config?.list[safe: selectedIndex], let iconPath = item.iconPath {
                setButtonIcon(button: previousButton, iconPath: iconPath, selected: false)
            }
        }
        
        // Update new selection
        selectedIndex = index
        let selectedButton = tabButtons[index]
        selectedButton.contentTintColor = getTabColor(selected: true)
        
        // Update icon if needed
        if let item = config?.list[safe: index] {
            let iconPath = item.selectedIconPath ?? item.iconPath
            if let iconPath = iconPath {
                setButtonIcon(button: selectedButton, iconPath: iconPath, selected: true)
            }
        }
        
        // Notify listener
        if notifyListener, let listener = onTabSelectedListener, let item = config?.list[safe: index] {
            listener(item.pagePath)
        }
    }
    
    @objc private func tabButtonTapped(_ sender: NSButton) {
        let index = sender.tag
        print("[macOSTabBar] Tab button tapped at index: \(index)")
        setSelectedTab(index: index, notifyListener: true)
    }

    // MARK: - Icon Loading
    private func getResourcesPath() -> String {
        let executablePath = Bundle.main.executablePath ?? ""
        let executableDir = (executablePath as NSString).deletingLastPathComponent
        return "\(executableDir)/Resources"
    }

    private func setButtonIcon(button: NSButton, iconPath: String, selected: Bool) {
        var image: NSImage?

        if iconPath.hasPrefix("SF:") {
            // System SF Symbol
            let symbolName = String(iconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)
                image?.isTemplate = true
            }
        } else if iconPath.hasPrefix("/") {
            // Absolute path - Rust layer should have already converted relative paths to absolute
            image = NSImage(contentsOfFile: iconPath)
            print("[macOSTabBar] Loading icon from absolute path: \(iconPath)")
        } else {
            // Try bundle first
            image = NSImage(named: iconPath)

            // If not found in bundle, try with appId in Resources directory
            if image == nil && !appId.isEmpty {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/\(appId)/\(iconPath)"
                image = NSImage(contentsOfFile: fullPath)
                print("[macOSTabBar] Trying to load icon from: \(fullPath)")
            }
        }

        if let image = image {
            let resizedImage = resizeImage(image, to: NSSize(width: TabBarConstants.ICON_SIZE, height: TabBarConstants.ICON_SIZE))
            button.image = resizedImage
            button.imagePosition = .imageAbove
            button.imageScaling = .scaleProportionallyDown
        } else {
            print("[macOSTabBar] Failed to load icon: \(iconPath)")
        }
    }

    private func resizeImage(_ image: NSImage, to size: NSSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        image.draw(in: NSRect(origin: .zero, size: size))
        resizedImage.unlockFocus()
        resizedImage.isTemplate = image.isTemplate
        return resizedImage
    }
}

#endif
