#if os(iOS)
import UIKit
import Foundation

/// iOS-specific tab bar implementation
public class iOSTabBar: UIView, TabBarProtocol {
    
    // MARK: - Properties
    public var onTabSelectedListener: ((String) -> Void)?

    private var stackView: UIStackView!
    private var tabButtons: [UIButton] = []
    private var tabItems: [TabBarItem] = []
    private var selectedIndex: Int = 0
    private var appId: String = ""
    
    // MARK: - Initialization
    public override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = TabBarConfig.DEFAULT_BACKGROUND_COLOR
        
        // Add top border
        let topBorder = UIView()
        topBorder.backgroundColor = NavigationBarConstants.BORDER_COLOR
        topBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(topBorder)
        
        NSLayoutConstraint.activate([
            topBorder.leadingAnchor.constraint(equalTo: leadingAnchor),
            topBorder.trailingAnchor.constraint(equalTo: trailingAnchor),
            topBorder.topAnchor.constraint(equalTo: topAnchor),
            topBorder.heightAnchor.constraint(equalToConstant: 0.5)
        ])
        
        // Create stack view for tab items
        stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .fill
        stackView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(stackView)
        
        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: topBorder.bottomAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }
    
    // MARK: - TabBarProtocol Implementation
    public func updateConfig(_ config: TabBarConfig, appId: String) {
        print("[iOSTabBar] updateConfig called with \(config.list.count) items for appId: \(appId)")

        self.appId = appId
        isHidden = config.hidden ?? false

        updateAppearance(config)

        tabItems = config.list
        rebuildTabButtons()
    }
    
    public func setSelectedTab(_ pagePath: String) {
        guard let index = tabItems.firstIndex(where: { $0.pagePath == pagePath }) else {
            return
        }
        
        selectedIndex = index
        updateButtonStates()
    }
    
    // MARK: - Private Methods
    private func rebuildTabButtons() {
        // Remove existing buttons
        tabButtons.forEach { $0.removeFromSuperview() }
        tabButtons.removeAll()
        
        // Create new buttons
        for (index, item) in tabItems.enumerated() {
            let button = createTabButton(for: item, at: index)
            tabButtons.append(button)
            stackView.addArrangedSubview(button)
        }
        
        updateButtonStates()
    }
    
    private func createTabButton(for item: TabBarItem, at index: Int) -> UIButton {
        let button = UIButton(type: .custom)
        button.tag = index
        button.addTarget(self, action: #selector(tabButtonTapped(_:)), for: .touchUpInside)

        // Create vertical stack for icon and text
        let containerView = UIView()
        containerView.isUserInteractionEnabled = false
        button.addSubview(containerView)
        containerView.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: button.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: button.centerYAnchor)
        ])

        // Icon image view
        let iconImageView = UIImageView()
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.tintColor = TabBarConfig.DEFAULT_COLOR
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(iconImageView)

        // Set icon if available
        if let iconPath = item.iconPath {
            setButtonIcon(imageView: iconImageView, iconPath: iconPath, selected: false)
        }

        // Text label (only if text is provided)
        var textLabel: UILabel?
        if let text = item.text, !text.isEmpty {
            textLabel = UILabel()
            textLabel!.text = text
            textLabel!.font = UIFont.systemFont(ofSize: TabBarConstants.ITEM_FONT_SIZE)
            textLabel!.textColor = TabBarConfig.DEFAULT_COLOR
            textLabel!.textAlignment = .center
            textLabel!.translatesAutoresizingMaskIntoConstraints = false
            containerView.addSubview(textLabel!)
        }

        // Layout constraints
        NSLayoutConstraint.activate([
            iconImageView.topAnchor.constraint(equalTo: containerView.topAnchor),
            iconImageView.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: TabBarConstants.ICON_SIZE),
            iconImageView.heightAnchor.constraint(equalToConstant: TabBarConstants.ICON_SIZE)
        ])

        if let textLabel = textLabel {
            NSLayoutConstraint.activate([
                textLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: TabBarConstants.ITEM_SPACING),
                textLabel.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                textLabel.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
                textLabel.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
            ])
        } else {
            iconImageView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor).isActive = true
        }

        return button
    }
    
    private func updateButtonStates() {
        for (index, button) in tabButtons.enumerated() {
            let isSelected = index == selectedIndex
            let item = tabItems[index]

            // Update colors and icons based on selection state
            if let containerView = button.subviews.first {
                let iconImageView = containerView.subviews.first as? UIImageView
                let textLabel = containerView.subviews.count > 1 ? containerView.subviews.last as? UILabel : nil

                let color = isSelected ? TabBarConfig.DEFAULT_SELECTED_COLOR : TabBarConfig.DEFAULT_COLOR
                iconImageView?.tintColor = color
                textLabel?.textColor = color

                // Update icon based on selection state
                if let iconImageView = iconImageView {
                    let iconPath = isSelected ? (item.selectedIconPath ?? item.iconPath) : item.iconPath
                    if let iconPath = iconPath {
                        setButtonIcon(imageView: iconImageView, iconPath: iconPath, selected: isSelected)
                    }
                }
            }
        }
    }
    
    @objc private func tabButtonTapped(_ sender: UIButton) {
        let index = sender.tag
        guard index < tabItems.count else { return }

        print("[iOSTabBar] Tab button tapped at index: \(index)")
        selectedIndex = index
        updateButtonStates()

        let selectedItem = tabItems[index]
        onTabSelectedListener?(selectedItem.pagePath)
    }

    // MARK: - Appearance Updates
    private func updateAppearance(_ config: TabBarConfig) {
        // Update background color
        if let backgroundColor = config.backgroundColor {
            let isTransparent = TabBarConfig.isTransparent(backgroundColor)

            if isTransparent {
                self.backgroundColor = UIColor.clear
            } else {
                self.backgroundColor = UIColor(hexString: backgroundColor) ?? TabBarConfig.DEFAULT_BACKGROUND_COLOR
            }
        } else {
            self.backgroundColor = TabBarConfig.DEFAULT_BACKGROUND_COLOR
        }
    }

    // MARK: - Icon Loading
    private func setButtonIcon(imageView: UIImageView, iconPath: String, selected: Bool) {
        var image: UIImage?

        if iconPath.hasPrefix("SF:") {
            // System SF Symbol
            let symbolName = String(iconPath.dropFirst(3))
            if #available(iOS 13.0, *) {
                image = UIImage(systemName: symbolName)
            }
        } else if iconPath.hasPrefix("/") {
            // Absolute path - Rust layer should have already converted relative paths to absolute
            image = UIImage(contentsOfFile: iconPath)
            print("[iOSTabBar] Loading icon from absolute path: \(iconPath)")
        } else {
            // Try bundle first
            image = UIImage(named: iconPath)

            // If not found in bundle, try with appId in main bundle
            if image == nil && !appId.isEmpty {
                if let bundlePath = Bundle.main.path(forResource: "\(appId)/\(iconPath)", ofType: nil) {
                    image = UIImage(contentsOfFile: bundlePath)
                    print("[iOSTabBar] Trying to load icon from: \(bundlePath)")
                }
            }
        }

        if let image = image {
            // Resize image to appropriate size
            let resizedImage = resizeImage(image, to: CGSize(width: TabBarConstants.ICON_SIZE, height: TabBarConstants.ICON_SIZE))
            imageView.image = resizedImage.withRenderingMode(.alwaysTemplate)
        } else {
            print("[iOSTabBar] Failed to load icon: \(iconPath)")
        }
    }

    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        image.draw(in: CGRect(origin: .zero, size: size))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return resizedImage ?? image
    }
}

#endif
