import Foundation

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Tab bar item configuration
public struct TabBarItem: Codable {
    public let text: String?
    public let iconPath: String?
    public let selectedIconPath: String?
    public let pagePath: String

    public init(text: String?, iconPath: String?, selectedIconPath: String?, pagePath: String) {
        self.text = text
        self.iconPath = iconPath
        self.selectedIconPath = selectedIconPath
        self.pagePath = pagePath
    }
}

/// Tab bar position enumeration
public enum TabBarPosition: String, Codable {
    case bottom = "bottom"
    case top = "top"
    case left = "left"
    case right = "right"
}

/// Tab bar configuration
public struct TabBarConfig: Codable {
    public let hidden: Bool?
    public let color: String?
    public let selectedColor: String?
    public let backgroundColor: String?
    public let borderStyle: String?
    public let list: [TabBarItem]
    public let position: TabBarPosition?
    public let custom: Bool?

    // Computed properties for backward compatibility
    var items: [TabBarItem] { return list }

    static let DEFAULT_COLOR = PlatformColor.platformSecondaryLabel
    static let DEFAULT_SELECTED_COLOR = PlatformColor.platformLabel
    static let DEFAULT_BACKGROUND_COLOR = PlatformColor.platformBackground

    public init(
        hidden: Bool? = false,
        color: String? = nil,
        selectedColor: String? = nil,
        backgroundColor: String? = nil,
        borderStyle: String? = nil,
        list: [TabBarItem] = [],
        position: TabBarPosition? = .bottom,
        custom: Bool? = false
    ) {
        self.hidden = hidden
        self.color = color
        self.selectedColor = selectedColor
        self.backgroundColor = backgroundColor
        self.borderStyle = borderStyle
        self.list = list
        self.position = position
        self.custom = custom
    }
    
    public static func fromJson(_ json: String?) -> TabBarConfig? {
        guard let json = json, !json.isEmpty else {
            return TabBarConfig(hidden: true)
        }

        do {
            guard let data = json.data(using: .utf8) else {
                return TabBarConfig(hidden: true)
            }

            return try JSONDecoder().decode(TabBarConfig.self, from: data)
        } catch {
            print("[TabBar] Failed to decode JSON: \(error)")
            return TabBarConfig(hidden: true)
        }
    }

    /// Check if background color is transparent
    public static func isTransparent(_ backgroundColor: String?) -> Bool {
        guard let backgroundColor = backgroundColor else { return false }
        return backgroundColor.lowercased() == "transparent" || backgroundColor.isEmpty
    }

    /// Get effective color with fallback
    func getEffectiveColor(_ colorString: String?, defaultColor: PlatformColor) -> PlatformColor {
        guard let colorString = colorString, !colorString.isEmpty else {
            return defaultColor
        }
        return PlatformColor(hexString: colorString) ?? defaultColor
    }
}

/// Protocol for tab bar implementations
@MainActor
public protocol TabBarProtocol: AnyObject {
    var onTabSelectedListener: ((String) -> Void)? { get set }

    func updateConfig(_ config: TabBarConfig)
    func setSelectedTab(_ pagePath: String)
}

/// Shared tab bar constants
public struct TabBarConstants {
    public static let ITEM_FONT_SIZE: CGFloat = 10
    public static let ICON_SIZE: CGFloat = 24
    public static let ITEM_SPACING: CGFloat = 4
    public static let BORDER_WIDTH: CGFloat = 0.5
    public static let TAB_HEIGHT: CGFloat = PLATFORM_TAB_BAR_HEIGHT
    public static let ICON_TOP_MARGIN: CGFloat = 8
    public static let LABEL_BOTTOM_MARGIN: CGFloat = 2
}

/// Helper extension for safe array access
extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
