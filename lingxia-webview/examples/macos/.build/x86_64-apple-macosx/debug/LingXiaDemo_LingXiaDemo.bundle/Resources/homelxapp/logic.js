(function() {
  "use strict";
  App({
    onLaunch: async function() {
      try {
        const response = await fetch("https://api64.ipify.org?format=json");
        const data = await response.json();
        this.globalData.ipAddr = data.ip;
        console.log("Got public address: ", data.ip);
      } catch (error) {
        this.globalData.ipAddr = error.message;
      }
      if (this.ipReadyCallback) {
        console.log("Calling IP ready callback");
        this.ipReadyCallback(this.globalData.ipAddr);
      }
    },
    globalData: {
      greeting: "This is from App's globalData.data",
      ipAddr: "loading"
    }
  });
  const app$1 = getApp();
  const globalData = app$1.globalData;
  Page({
    data: {
      greeting: globalData.greeting,
      imageUrl: "../../images/futuristic.jpg",
      ipAddr: globalData.ipAddr,
      greetCount: 0
    },
    onReady: function() {
      app$1.ipReadyCallback = (ip) => {
        console.log("IP received in Page:", ip);
        this.setData({
          ipAddr: ip
        });
      };
      if (app$1.globalData.ipAddr) {
        this.setData({
          ipAddr: app$1.globalData.ipAddr
        });
      }
    },
    onUnload: function() {
      console.log("onUnload: -----");
    },
    onLoad: async function() {
      console.log("onLoad: ------");
      console.log(lx.env.USER_CACHE_PATH);
      let testFile = `${lx.env.USER_CACHE_PATH}/testFile`;
      const testContent = "Hello, World!";
      await Rong.writeTextFile(testFile, testContent);
      try {
        await Rong.mkdir(
          `/data/storage/el2/base/cache/lingxia/usercache/testminiapp`,
          {
            recursive: true
          }
        );
      } catch (e) {
        console.log(e);
      }
    },
    onHide: function() {
      console.log("onHide: ------");
    },
    onShow: function() {
      console.log("onShow: +++++++");
    },
    greet: async function(option) {
      const count = this.data.greetCount + 1;
      await this.setData(
        {
          greeting: `👋 Hello ${option.name}! (#${count})

🌍 Greetings from appservice powered by Rust and JS engine
🕒 ${(/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", { hour: "2-digit", minute: "2-digit", second: "2-digit" })}`,
          greetCount: count
        },
        () => {
          console.log("setData callback");
        }
      );
    }
  }, "pages/home/<USER>");
  const app = getApp();
  Page({
    onLoad: function() {
    },
    onShow: function() {
      console.log("API page onShow");
      console.log("App data:", app.globalData);
    },
    onHide: function() {
      console.log("API page onHide");
    },
    openLxApp: function(option) {
      console.log("getDeviceInfo:", JSON.stringify(lx.getDeviceInfo()));
      lx.navigateToMiniProgram(option);
    }
  }, "pages/API/index.tsx");
  var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
  var freeSelf = typeof self == "object" && self && self.Object === Object && self;
  var root = freeGlobal || freeSelf || Function("return this")();
  var Symbol$1 = root.Symbol;
  var symbolProto = Symbol$1 ? Symbol$1.prototype : void 0;
  symbolProto ? symbolProto.toString : void 0;
  function baseToString(value) {
    {
      return value;
    }
  }
  function toString(value) {
    return baseToString(value);
  }
  var idCounter = 0;
  function uniqueId(prefix) {
    var id = ++idCounter;
    return toString(prefix) + id;
  }
  function generateTodoId() {
    return uniqueId("todo_");
  }
  function validateTodoText(text) {
    return text && typeof text === "string" && text.trim().length > 0;
  }
  function getCurrentTimestamp() {
    return (/* @__PURE__ */ new Date()).toISOString();
  }
  function getInitialData() {
    try {
      const storedTodos = Rong.storage.get("todo:todos");
      const storedFilter = Rong.storage.get("todo:filter");
      const storedLastUpdated = Rong.storage.get("todo:lastUpdated");
      if (storedTodos && Array.isArray(storedTodos) && storedTodos.length > 0) {
        console.log("[Todo] Found stored todos:", storedTodos.length);
        return {
          todos: storedTodos,
          currentFilter: storedFilter || "all",
          lastUpdated: storedLastUpdated || getCurrentTimestamp()
        };
      } else {
        console.log("[Todo] No stored todos found, using empty state");
        return {
          todos: [],
          currentFilter: "all",
          lastUpdated: getCurrentTimestamp()
        };
      }
    } catch (error) {
      console.error("[Todo] Error loading initial data:", error);
      return {
        todos: [],
        currentFilter: "all",
        lastUpdated: getCurrentTimestamp()
      };
    }
  }
  Page({
    data: getInitialData(),
    // Storage keys
    STORAGE_KEYS: {
      TODOS: "todo:todos",
      FILTER: "todo:filter",
      LAST_UPDATED: "todo:lastUpdated"
    },
    // Page lifecycle
    onLoad: function() {
      console.log("[Todo] Page loaded, initial todos:", this.data.todos.length);
    },
    onReady: function() {
      console.log("[Todo] Page ready");
    },
    onShow: function() {
      console.log("[Todo] Page shown");
      if (typeof LingXiaUtils !== "undefined" && LingXiaUtils.generateTodoId) {
        const testId = LingXiaUtils.generateTodoId();
        console.log("[Todo] Generated test UUID using bundled utilities:", testId);
      } else {
        console.log("[Todo] LingXiaUtils utilities not available");
      }
    },
    _saveToStorage: function() {
      try {
        Rong.storage.set(this.STORAGE_KEYS.TODOS, this.data.todos);
        Rong.storage.set(this.STORAGE_KEYS.FILTER, this.data.currentFilter);
        Rong.storage.set(this.STORAGE_KEYS.LAST_UPDATED, this.data.lastUpdated);
        console.log("[Todo] saveToStorage: Successfully saved to storage");
      } catch (error) {
        console.error("[Todo] saveToStorage: Error saving to storage:", error);
      }
    },
    _clearStorage: function() {
      try {
        Rong.storage.delete(this.STORAGE_KEYS.TODOS);
        Rong.storage.delete(this.STORAGE_KEYS.FILTER);
        Rong.storage.delete(this.STORAGE_KEYS.LAST_UPDATED);
        console.log("[Todo] clearStorage: Successfully cleared storage");
      } catch (error) {
        console.error("[Todo] clearStorage: Error clearing storage:", error);
      }
    },
    // Todo core functionality
    addTodo: async function(params) {
      const { text } = params;
      if (!validateTodoText(text)) {
        console.log("[Todo] addTodo: Invalid text, skipping");
        return;
      }
      const newTodo = {
        id: generateTodoId(),
        text: text.trim(),
        completed: false
      };
      console.log("[Todo] addTodo: Adding new todo:", newTodo);
      await this.setData({
        todos: [...this.data.todos, newTodo],
        lastUpdated: getCurrentTimestamp()
      });
      this._saveToStorage();
      console.log(
        "[Todo] addTodo: Todo added successfully, total todos:",
        this.data.todos.length
      );
    },
    toggleTodo: async function(params) {
      const { id } = params;
      if (!id) {
        console.log("[Todo] toggleTodo: No ID provided, skipping");
        return;
      }
      const targetTodo = this.data.todos.find((todo) => todo.id === id);
      console.log("[Todo] toggleTodo: Target todo:", targetTodo);
      const updatedTodos = this.data.todos.map(
        (todo) => todo.id === id ? { ...todo, completed: !todo.completed } : todo
      );
      await this.setData({
        todos: updatedTodos,
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      });
      this._saveToStorage();
      console.log("[Todo] toggleTodo: Todo toggled successfully");
    },
    deleteTodo: async function(params) {
      const { id } = params;
      if (!id) {
        console.log("[Todo] deleteTodo: No ID provided, skipping");
        return;
      }
      const targetTodo = this.data.todos.find((todo) => todo.id === id);
      console.log("[Todo] deleteTodo: Deleting todo:", targetTodo);
      const updatedTodos = this.data.todos.filter((todo) => todo.id !== id);
      await this.setData({
        todos: updatedTodos,
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      });
      this._saveToStorage();
      console.log(
        "[Todo] deleteTodo: Todo deleted successfully, remaining todos:",
        updatedTodos.length
      );
    },
    clearCompleted: async function() {
      const completedCount = this.data.todos.filter(
        (todo) => todo.completed
      ).length;
      console.log(
        "[Todo] clearCompleted: Clearing",
        completedCount,
        "completed todos"
      );
      const updatedTodos = this.data.todos.filter((todo) => !todo.completed);
      await this.setData({
        todos: updatedTodos,
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      });
      this._saveToStorage();
      console.log(
        "[Todo] clearCompleted: Completed todos cleared, remaining todos:",
        updatedTodos.length
      );
    },
    setFilter: async function(params) {
      console.log("[Todo] setFilter called with params:", params);
      const { filter } = params;
      if (!filter || !["all", "active", "completed"].includes(filter)) {
        console.log("[Todo] setFilter: Invalid filter value, skipping");
        return;
      }
      console.log("[Todo] setFilter: Setting filter to:", filter);
      await this.setData({
        currentFilter: filter,
        lastUpdated: (/* @__PURE__ */ new Date()).toISOString()
      });
      this._saveToStorage();
      console.log("[Todo] setFilter: Filter set successfully");
    },
    // Debug method (can be removed in production)
    _getStorageInfo: function() {
      try {
        const info = Rong.storage.info();
        console.log("[Todo] Storage info:", info);
        return info;
      } catch (error) {
        console.error(
          "[Todo] getStorageInfo: Error getting storage info:",
          error
        );
        return null;
      }
    }
  }, "pages/todo/index.vue");
})();
//# sourceMappingURL=main.iife.js.map
