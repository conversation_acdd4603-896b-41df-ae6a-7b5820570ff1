{"version": 3, "names": ["_defineAccessor", "type", "obj", "key", "fn", "desc", "configurable", "enumerable", "Object", "defineProperty"], "sources": ["../../src/helpers/defineAccessor.ts"], "sourcesContent": ["/* @minVersion 7.20.7 */\n\nexport default function _defineAccessor<Type extends \"get\" | \"set\">(\n  type: Type,\n  obj: any,\n  key: string | symbol,\n  fn: PropertyDescriptor[Type],\n) {\n  var desc: PropertyDescriptor = { configurable: true, enumerable: true };\n  desc[type] = fn;\n  return Object.defineProperty(obj, key, desc);\n}\n"], "mappings": ";;;;;;AAEe,SAASA,eAAeA,CACrCC,IAAU,EACVC,GAAQ,EACRC,GAAoB,EACpBC,EAA4B,EAC5B;EACA,IAAIC,IAAwB,GAAG;IAAEC,YAAY,EAAE,IAAI;IAAEC,UAAU,EAAE;EAAK,CAAC;EACvEF,IAAI,CAACJ,IAAI,CAAC,GAAGG,EAAE;EACf,OAAOI,MAAM,CAACC,cAAc,CAACP,GAAG,EAAEC,GAAG,EAAEE,IAAI,CAAC;AAC9C", "ignoreList": []}