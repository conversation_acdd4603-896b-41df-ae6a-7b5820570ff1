{"version": 3, "names": ["_readOnly<PERSON><PERSON>r", "name", "TypeError"], "sources": ["../../src/helpers/readOnlyError.ts"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nexport default function _readOnlyError(name: string) {\n  throw new TypeError('\"' + name + '\" is read-only');\n}\n"], "mappings": ";;;;;;AAEe,SAASA,cAAcA,CAACC,IAAY,EAAE;EACnD,MAAM,IAAIC,SAAS,CAAC,GAAG,GAAGD,IAAI,GAAG,gBAAgB,CAAC;AACpD", "ignoreList": []}