"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _superPropBase;
var _getPrototypeOf = require("./getPrototypeOf.js");
function _superPropBase(object, property) {
  while (!Object.prototype.hasOwnProperty.call(object, property)) {
    object = (0, _getPrototypeOf.default)(object);
    if (object === null) break;
  }
  return object;
}

//# sourceMappingURL=superPropBase.js.map
