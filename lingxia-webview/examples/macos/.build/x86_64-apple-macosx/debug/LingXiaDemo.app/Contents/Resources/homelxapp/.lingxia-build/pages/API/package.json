{"name": "lingxia-react-page", "version": "1.0.0", "type": "module", "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "lodash-es": "^4.17.21", "vue": "^3.5.13", "clsx": "^2.1.1", "class-variance-authority": "^0.7.0", "@radix-ui/react-slot": "^1.1.0", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.21"}, "devDependencies": {"vite": "^6.0.7", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-vue": "^5.2.1", "typescript": "^5.0.0"}, "scripts": {"build": "vite build", "dev": "vite"}}