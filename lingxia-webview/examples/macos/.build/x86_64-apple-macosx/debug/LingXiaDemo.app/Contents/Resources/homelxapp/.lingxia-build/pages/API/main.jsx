import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'

window.useLingXiaData = function () {
  const [data, setData] = React.useState({});

  React.useEffect(() => {
    if (window.LingXiaBridge && window.LingXiaBridge.subscribe) {
      window.LingXiaBridge.subscribe((newData) => {
        if (newData) {
          setData(prevData => ({ ...prevData, ...newData }));
        }
      });
    }
  }, []);

  return data;
};

// Page functions injection
window.__PAGE_FUNCTIONS = ["openLxApp"];

// Generate bridge functions
window.__PAGE_FUNCTIONS.forEach(function(funcName) {
  window[funcName] = function(...args) {
    return window.LingXiaBridge.call(funcName, args.length === 1 ? args[0] : args);
  };
});

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
