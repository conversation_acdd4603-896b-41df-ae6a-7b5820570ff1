import Cocoa
import lingxia
import os.log

// Global logger for app-wide logging
let appLog = OSLog(subsystem: "LingXia.LxApp.macOS", category: "App")

@MainActor
class AppDelegate: NSObject, NSApplicationDelegate {
    private static var hasInitialized = false
    private let log = appLog

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        print("DEBUG: AppDelegate: applicationDidFinishLaunching called")
        
        // Disable window tabbing for better window management
        NSWindow.allowsAutomaticWindowTabbing = false

        // Ensure application activation
        NSApp.setActivationPolicy(.regular)
        NSApp.activate(ignoringOtherApps: true)

        if !Self.hasInitialized {
            Self.hasInitialized = true

            print("DEBUG: AppDelegate: Initializing LxApps...")
            // Initialize LxApps
            macOSLxApp.ensureLxAppsInitialized()

            // Check if initialization was successful
            if macOSLxApp.isInitialized {
                print("DEBUG: AppDelegate: Setting window size...")
                // Set window size for iPhone-like UI (fixed size to prevent user resizing)
                macOSLxApp.setWindowSize(width: 414, height: 896, fixed: true)

                // Test dynamic resizing after 3 seconds
                DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                    print("DEBUG: Testing dynamic window resize...")
                    macOSLxApp.setWindowSize(width: 600, height: 800, fixed: false)
                }

                print("DEBUG: AppDelegate: Opening home LxApp window...")

                // Open main application
                macOSLxApp.openHomeLxApp()

                // Force app activation immediately
                NSApp.activate(ignoringOtherApps: true)

                // Bring all windows to front
                for window in NSApp.windows {
                    window.orderFrontRegardless()
                }
            } else {
                print("ERROR: LxApps initialization failed!")
                os_log("LxApps initialization failed!", log: log, type: .error)
            }
        }
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        print("DEBUG: AppDelegate: applicationWillTerminate called")
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }
}

// Create application and delegate
let app = NSApplication.shared
let appDelegate = AppDelegate()
app.delegate = appDelegate

// Start application main run loop
print("DEBUG: Starting application main run loop")
app.run()
