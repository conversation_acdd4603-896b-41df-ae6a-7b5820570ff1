import Cocoa
import os.log

// Log instance outside of @MainActor to avoid isolation issues
private let tabBarLog = OSLog(subsystem: "LingXia", category: "TabBar")

/// TabBar configuration structure
public struct TabBarConfig: Codable {
    public let list: [TabBarItem]
    public let color: String?
    public let selectedColor: String?
    public let backgroundColor: String?
    public let borderStyle: String?
    public let position: TabBarPosition?
    public let custom: Bool?

    public static func fromJson(_ json: String?) -> TabBarConfig? {
        guard let json = json,
              let data = json.data(using: .utf8) else {
            return nil
        }
        
        return try? JSONDecoder().decode(TabBarConfig.self, from: data)
    }

    public static func isTransparent(_ backgroundColor: String?) -> Bool {
        guard let backgroundColor = backgroundColor else { return false }
        return backgroundColor.lowercased() == "transparent" || backgroundColor.isEmpty
    }
}

/// TabBar item structure
public struct TabBarItem: Codable {
    public let pagePath: String
    public let text: String?
    public let iconPath: String?
    public let selectedIconPath: String?
}

/// TabBar position enumeration
public enum TabBarPosition: String, Codable {
    case bottom = "bottom"
    case top = "top"
    case left = "left"
    case right = "right"
}

/// Custom button that responds to clicks in the entire button area
class TabBarButton: NSButton {
    // 移除自定义hitTest方法，使用NSButton默认的点击检测
    // NSStackView会正确处理坐标转换和点击区域检测
}

/// macOS TabBar implementation
@MainActor
public class LingXiaTabBar: NSView {
    private static let log = tabBarLog

    // MARK: - Properties
    internal var config: TabBarConfig!
    private var tabButtons: [NSButton] = []
    private var selectedIndex: Int = 0
    private var onTabSelectedListener: ((Int, String) -> Void)?

    // MARK: - Constants
    private static let TAB_BUTTON_HEIGHT: CGFloat = 40    // Optimized tab bar height
    private static let TAB_ICON_SIZE: CGFloat = 24        // WeChat style icon size
    private static let TAB_SPACING: CGFloat = 4           // Reduced spacing between icon and text
    private static let TAB_LABEL_HEIGHT: CGFloat = 14     // WeChat style label height
    private static let TAB_LABEL_FONT_SIZE: CGFloat = 10  // WeChat style font size
    private static let TAB_ICON_TOP_MARGIN: CGFloat = 10  // Increased to move icon down slightly
    private static let TAB_LABEL_BOTTOM_MARGIN: CGFloat = 2 // WeChat style label bottom margin

    public override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        setupView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        wantsLayer = true
        // Background color will be set by updateAppearance() based on configuration

        // Add top border for separation
        let topBorder = NSView()
        topBorder.wantsLayer = true
        topBorder.layer?.backgroundColor = NSColor.separatorColor.cgColor
        topBorder.translatesAutoresizingMaskIntoConstraints = false
        addSubview(topBorder)

        topBorder.leadingAnchor.constraint(equalTo: leadingAnchor).isActive = true
        topBorder.trailingAnchor.constraint(equalTo: trailingAnchor).isActive = true
        topBorder.topAnchor.constraint(equalTo: topAnchor).isActive = true
        topBorder.heightAnchor.constraint(equalToConstant: 0.5).isActive = true
    }

    /// Set TabBar configuration
    public func setConfig(config: TabBarConfig) {
        print("[TabBar] setConfig called with:")
        print("[TabBar]   backgroundColor: \(config.backgroundColor ?? "nil")")
        print("[TabBar]   color: \(config.color ?? "nil")")
        print("[TabBar]   selectedColor: \(config.selectedColor ?? "nil")")
        print("[TabBar]   borderStyle: \(config.borderStyle ?? "nil")")
        print("[TabBar]   list count: \(config.list.count)")
        
        self.config = config
        setupTabButtons()
        updateAppearance()
    }

    /// Set tab selection listener
    public func setOnTabSelectedListener(_ listener: @escaping (Int, String) -> Void) {
        self.onTabSelectedListener = listener
    }

    /// Sync selected tab with current path
    public func syncSelectedTabWithCurrentPath(_ currentPath: String) {
        guard let config = config else { return }

        for (index, item) in config.list.enumerated() {
            if item.pagePath == currentPath {
                setSelectedTab(index: index, notifyListener: false)
                break
            }
        }
    }

    private func setupTabButtons() {
        // Remove existing buttons
        tabButtons.forEach { $0.removeFromSuperview() }
        tabButtons.removeAll()

        guard let config = config else { return }

        // Create tab buttons
        for (index, item) in config.list.enumerated() {
            let button = createTabButton(for: item, at: index)
            tabButtons.append(button)
            addSubview(button)
        }

        // Setup layout
        layoutTabButtons()

        // Set initial selection
        if !config.list.isEmpty {
            setSelectedTab(index: 0, notifyListener: false)
        }
    }

    private func createTabButton(for item: TabBarItem, at index: Int) -> NSButton {
        let button = TabBarButton()  // Use custom button class
        button.title = item.text ?? ""
        button.font = NSFont.systemFont(ofSize: Self.TAB_LABEL_FONT_SIZE, weight: .medium)
        button.isBordered = false
        button.target = self
        button.action = #selector(tabButtonTapped(_:))
        button.tag = index
        button.translatesAutoresizingMaskIntoConstraints = false

        // Style button with WeChat-like appearance
        button.wantsLayer = true
        button.layer?.backgroundColor = NSColor.clear.cgColor
        button.contentTintColor = getTabColor(selected: false)

        // Set icon if available
        if item.iconPath != nil {
            setButtonIcon(button: button, iconPath: item.iconPath!, selected: false)
        }

        // Set button layout
        button.imagePosition = .imageAbove
        button.imageScaling = .scaleProportionallyDown
        
        return button
    }

    private func getResourcesPath() -> String {
        // Get the path to the Resources directory
        let executablePath = Bundle.main.executablePath ?? ""
        let executableDir = (executablePath as NSString).deletingLastPathComponent
        return "\(executableDir)/Resources"
    }

    private func setButtonIcon(button: NSButton, iconPath: String, selected: Bool) {
        // Try to load icon from various sources
        var image: NSImage?

        if iconPath.hasPrefix("SF:") {
            // System SF Symbol
            let symbolName = String(iconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)
                if let image = image {
                    image.isTemplate = true
                }
            }
        } else if iconPath.hasPrefix("/") {
            // Absolute path - load from file system
            image = NSImage(contentsOfFile: iconPath)
            if image == nil {
                print("[TabBar] Failed to load icon from absolute path: \(iconPath)")
            }
        } else {
            // Try to load from bundle first
            image = NSImage(named: iconPath)

            // If not found in bundle, try as relative path from Resources
            if image == nil {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/homeminiapp/\(iconPath)"
                print("[TabBar] Trying to load icon from: \(fullPath)")
                image = NSImage(contentsOfFile: fullPath)
                if image == nil {
                    print("[TabBar] Failed to load icon from path: \(fullPath)")
                } else {
                    print("[TabBar] Successfully loaded icon from: \(fullPath)")
                }
            }
        }

        if let image = image {
            // Resize image to appropriate size for tab bar
            let resizedImage = resizeImage(image, to: NSSize(width: Self.TAB_ICON_SIZE, height: Self.TAB_ICON_SIZE))
            button.image = resizedImage
            button.imagePosition = .imageAbove
            button.imageScaling = .scaleProportionallyDown
        } else {
            // Fallback to text-only if no icon could be loaded
            print("[TabBar] No icon loaded for path: \(iconPath), using text-only button")
        }
    }

    private func resizeImage(_ image: NSImage, to size: NSSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        image.draw(in: NSRect(origin: .zero, size: size))
        resizedImage.unlockFocus()
        
        // 保持原图像的模板状态
        resizedImage.isTemplate = image.isTemplate
        
        return resizedImage
    }

    private func layoutTabButtons() {
        guard !tabButtons.isEmpty else { return }

        let isVertical = config.position == .left || config.position == .right
        
        if isVertical {
            layoutVerticalTabs()
        } else {
            layoutHorizontalTabs()
        }
    }

    private func layoutHorizontalTabs() {
        // Remove fixed width constraints to allow equal distribution
        for button in tabButtons {
            NSLayoutConstraint.activate([
                button.heightAnchor.constraint(equalTo: heightAnchor)
            ])
        }
        
        let stackView = NSStackView(views: tabButtons)
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8  // Small spacing between buttons
        stackView.translatesAutoresizingMaskIntoConstraints = false

        addSubview(stackView)
        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 16),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -16),
            stackView.topAnchor.constraint(equalTo: topAnchor),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    private func layoutVerticalTabs() {
        // Set fixed height for each button, reduced for compact layout
        let buttonHeight: CGFloat = 50
        let buttonSpacing: CGFloat = 10
        let horizontalInset: CGFloat = 4 // Further reduced inset for even closer to edge
        
        for button in tabButtons {
            NSLayoutConstraint.activate([
                button.heightAnchor.constraint(equalToConstant: buttonHeight)
                // Remove width constraint to allow manual inset control
            ])
        }
        
        let stackView = NSStackView(views: tabButtons)
        stackView.orientation = .vertical
        stackView.distribution = .equalSpacing
        stackView.spacing = buttonSpacing
        stackView.translatesAutoresizingMaskIntoConstraints = false

        addSubview(stackView)
        NSLayoutConstraint.activate([
            stackView.centerYAnchor.constraint(equalTo: centerYAnchor),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: horizontalInset),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -horizontalInset)
        ])
    }

    private func updateAppearance() {
        guard let config = config else { return }

        // Update background color
        print("[TabBar] updateAppearance called")
        if let backgroundColor = config.backgroundColor {
            print("[TabBar] backgroundColor from config: \(backgroundColor)")
            let isTransparent = TabBarConfig.isTransparent(backgroundColor)
            print("[TabBar] isTransparent check result: \(isTransparent)")
            
            if isTransparent {
                print("[TabBar] Setting transparent background")
                layer?.backgroundColor = NSColor.clear.cgColor
            } else {
                let parsedColor = NSColor(hexString: backgroundColor)
                print("[TabBar] Parsed color from hex '\(backgroundColor)': \(parsedColor?.description ?? "nil")")
                let finalColor = parsedColor?.cgColor ?? NSColor.controlBackgroundColor.cgColor
                print("[TabBar] Setting background color to: \(finalColor)")
                layer?.backgroundColor = finalColor
            }
        } else {
            print("[TabBar] No backgroundColor in config, using default")
            layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor
        }

        // Update border style
        if let borderStyle = config.borderStyle, borderStyle != "none" {
            layer?.borderWidth = 1.0
            layer?.borderColor = NSColor.separatorColor.cgColor
        }
    }

    private func getTabColor(selected: Bool) -> NSColor {
        if selected {
            if let selectedColor = config.selectedColor {
                return NSColor(hexString: selectedColor) ?? NSColor.controlAccentColor
            }
            return NSColor.controlAccentColor
        } else {
            if let color = config.color {
                return NSColor(hexString: color) ?? NSColor.secondaryLabelColor
            }
            return NSColor.secondaryLabelColor
        }
    }

    private func setSelectedTab(index: Int, notifyListener: Bool = true) {
        guard index >= 0 && index < tabButtons.count else { return }

        // Update previous selection
        if selectedIndex < tabButtons.count {
            let previousButton = tabButtons[selectedIndex]
            previousButton.contentTintColor = getTabColor(selected: false)
            
            // Update icon if needed
            if let item = config.list[safe: selectedIndex], let iconPath = item.iconPath {
                setButtonIcon(button: previousButton, iconPath: iconPath, selected: false)
            }
        }

        // Update new selection
        selectedIndex = index
        let selectedButton = tabButtons[index]
        selectedButton.contentTintColor = getTabColor(selected: true)

        // Update icon if needed
        if let item = config.list[safe: index] {
            let iconPath = item.selectedIconPath ?? item.iconPath
            if let iconPath = iconPath {
                setButtonIcon(button: selectedButton, iconPath: iconPath, selected: true)
            }
        }

        // Notify listener
        if notifyListener, let listener = onTabSelectedListener, let item = config.list[safe: index] {
            listener(index, item.pagePath)
        }
    }

    @objc private func tabButtonTapped(_ sender: NSButton) {
        let index = sender.tag
        print("[TABBAR ACTION] Tab button tapped at index: \(index)")
        os_log("Tab button tapped at index: %d", log: Self.log, type: .info, index)
        
        // 添加更详细的调试信息
        if let item = config.list[safe: index] {
            print("[TABBAR ACTION] Switching to page: \(item.pagePath)")
        }
        
        setSelectedTab(index: index, notifyListener: true)
        print("[TABBAR ACTION] Tab selection completed")
    }
}

// MARK: - Extensions
extension NSColor {
    convenience init?(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            return nil
        }

        self.init(
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            alpha: Double(a) / 255
        )
    }
}

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
